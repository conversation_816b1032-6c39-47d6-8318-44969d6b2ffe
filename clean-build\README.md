# Go Music Player

A desktop music player application built with Go, featuring a modern GUI and support for multiple audio formats.

## Features

### Core Functionality
- **Audio Playback**: Play, pause, stop, and skip audio files
- **Format Support**: MP3, WAV, and FLAC audio formats
- **Volume Control**: Adjustable volume with slider control
- **Progress Bar**: Visual playback progress with seek functionality
- **Song Information**: Display current song title, artist, and duration

### User Interface
- **Modern GUI**: Built with Fyne framework for cross-platform compatibility
- **Media Controls**: Standard play/pause, stop, previous/next buttons
- **File Browser**: Easy file selection and playlist management
- **Playlist View**: Visual playlist with clickable song selection
- **Clean Layout**: Intuitive design optimized for desktop use

### Technical Features
- **Go Language**: Written entirely in Go for performance and reliability
- **Windows Compatible**: Optimized for Windows operating system
- **Audio Libraries**: Uses Beep library with Oto backend for audio playback
- **File I/O**: Robust file handling for local audio files

## Requirements

- **Operating System**: Windows 10 or later
- **Go Version**: Go 1.19 or later
- **C Compiler**: TDM-GCC or MinGW-w64 (required for CGO)
- **Audio System**: Windows audio drivers

## Installation

### Prerequisites

Before building the application, you need to install the required tools:

1. **Install Go** (if not already installed):
   - Download from https://golang.org/dl/
   - Choose the Windows installer for your architecture (64-bit recommended)
   - Follow the installation instructions
   - Verify installation by opening Command Prompt and running: `go version`

2. **Install TDM-GCC** (required for building GUI applications):
   - Download TDM-GCC from: https://jmeubank.github.io/tdm-gcc/download/
   - Download `tdm64-gcc-10.3.0-2.exe` (64-bit version recommended)
   - Run the installer with default settings
   - **Important**: Restart your Command Prompt after installation
   - Verify installation by running: `gcc --version`

### Option 1: Automated Build (Recommended)

1. **Download** this project to your local machine

2. **Open Command Prompt** and navigate to the project directory:
   ```cmd
   cd path\to\music-player
   ```

3. **Run the build script**:
   ```cmd
   build.bat
   ```

   This script will:
   - Check if Go and GCC are properly installed
   - Download all dependencies
   - Build the application
   - Create `music-player.exe`

### Option 2: Manual Build

1. **Navigate to project directory** in Command Prompt:
   ```cmd
   cd path\to\music-player
   ```

2. **Install dependencies**:
   ```cmd
   go mod tidy
   ```

3. **Build the application**:
   ```cmd
   set CGO_ENABLED=1
   go build -o music-player.exe
   ```

4. **Run the application**:
   ```cmd
   music-player.exe
   ```

### Option 2: Direct Run (Development)

1. **Navigate to project directory** in Command Prompt

2. **Run directly with Go**:
   ```cmd
   go run main.go
   ```

## Usage

### Getting Started

1. **Launch the application** by running `music-player.exe`

2. **Add music files**:
   - Click the "Open Files" button
   - Select one or more audio files (MP3, WAV, or FLAC)
   - Files will be added to the playlist on the right side

3. **Control playback**:
   - **Play/Pause**: Click the Play/Pause button or select a song from the playlist
   - **Stop**: Click the Stop button to stop playback and reset position
   - **Previous/Next**: Navigate through the playlist
   - **Volume**: Adjust using the volume slider
   - **Seek**: Click on the progress bar to jump to a specific position

### Supported File Formats

- **MP3**: MPEG Audio Layer III
- **WAV**: Waveform Audio File Format
- **FLAC**: Free Lossless Audio Codec

### Keyboard Shortcuts

Currently, the application uses mouse/click controls. Keyboard shortcuts may be added in future versions.

## Project Structure

```
music-player/
├── main.go              # Application entry point
├── go.mod              # Go module definition
├── go.sum              # Dependency checksums
├── README.md           # This documentation
└── player/             # Core player package
    ├── audio.go        # Audio playback engine
    ├── playlist.go     # Playlist management
    └── ui.go          # GUI interface
```

## Dependencies

- **fyne.io/fyne/v2**: Cross-platform GUI framework
- **github.com/gopxl/beep**: Audio playback library
- **github.com/gopxl/beep/mp3**: MP3 format support
- **github.com/gopxl/beep/wav**: WAV format support
- **github.com/gopxl/beep/flac**: FLAC format support
- **github.com/gopxl/beep/speaker**: Audio output handling

## Troubleshooting

### Common Issues

1. **"No audio device found"**:
   - Ensure your audio drivers are properly installed
   - Check that your speakers/headphones are connected
   - Try restarting the application

2. **"Failed to decode audio"**:
   - Verify the audio file is not corrupted
   - Ensure the file format is supported (MP3, WAV, FLAC)
   - Try with a different audio file

3. **Application won't start**:
   - Ensure Go is properly installed
   - Run `go mod tidy` to install dependencies
   - Check for any error messages in the command prompt

### Performance Tips

- **Large Files**: Very large audio files may take longer to load
- **Multiple Files**: Adding many files at once may cause temporary UI freezing
- **Memory Usage**: The application loads entire audio files into memory

## Development

### Building for Distribution

To create a standalone executable:

```cmd
go build -ldflags="-s -w" -o music-player.exe
```

The `-ldflags="-s -w"` flags reduce the executable size by stripping debug information.

### Adding Features

The modular design makes it easy to extend:

- **Audio formats**: Add new decoders in `audio.go`
- **UI components**: Extend the interface in `ui.go`
- **Playlist features**: Enhance playlist management in `playlist.go`

## License

This project is provided as-is for educational and personal use.

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve the music player.
