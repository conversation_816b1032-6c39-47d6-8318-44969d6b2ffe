@echo off
echo Building Go Music Player...
echo.

REM Check if Go is installed
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Go is not installed or not in PATH
    echo Please install Go from https://golang.org/dl/
    pause
    exit /b 1
)

REM Check if GCC is available
gcc --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: GCC compiler not found
    echo.
    echo To build this application, you need a C compiler.
    echo Please install TDM-GCC from: https://jmeubank.github.io/tdm-gcc/download/
    echo.
    echo 1. Download tdm64-gcc-10.3.0-2.exe
    echo 2. Run the installer with default settings
    echo 3. Restart your command prompt
    echo 4. Run this script again
    echo.
    pause
    exit /b 1
)

echo Go and GCC found. Building application...
echo.

REM Set CGO_ENABLED and build
set CGO_ENABLED=1
go mod tidy
if %errorlevel% neq 0 (
    echo Error: Failed to download dependencies
    pause
    exit /b 1
)

go build -o music-player.exe
if %errorlevel% neq 0 (
    echo Error: Build failed
    pause
    exit /b 1
)

echo.
echo Build successful! music-player.exe created.
echo You can now run the music player by double-clicking music-player.exe
echo.
pause
