package player

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/gopxl/beep"
	"github.com/gopxl/beep/flac"
	"github.com/gopxl/beep/mp3"
	"github.com/gopxl/beep/speaker"
	"github.com/gopxl/beep/wav"
)

// AudioPlayer represents the main audio player
type AudioPlayer struct {
	mu              sync.RWMutex
	streamer        beep.StreamSeekCloser
	ctrl            *beep.Ctrl
	volume          *beep.Volume
	format          beep.Format
	currentFile     string
	isPlaying       bool
	isPaused        bool
	duration        time.Duration
	position        time.Duration
	onPositionChange func(time.Duration)
	onSongEnd       func()
}

// SongInfo contains metadata about the current song
type SongInfo struct {
	Title    string
	Artist   string
	Duration time.Duration
	Filename string
}

// NewAudioPlayer creates a new audio player instance
func NewAudioPlayer() *AudioPlayer {
	return &AudioPlayer{
		onPositionChange: func(time.Duration) {},
		onSongEnd:       func() {},
	}
}

// LoadFile loads an audio file for playback
func (ap *AudioPlayer) LoadFile(filename string) error {
	ap.mu.Lock()
	defer ap.mu.Unlock()

	// Stop current playback if any
	if ap.streamer != nil {
		ap.streamer.Close()
	}

	// Open the audio file
	file, err := os.Open(filename)
	if err != nil {
		return fmt.Errorf("failed to open file: %w", err)
	}

	// Determine file type and decode accordingly
	ext := strings.ToLower(filepath.Ext(filename))
	var streamer beep.StreamSeekCloser
	var format beep.Format

	switch ext {
	case ".mp3":
		streamer, format, err = mp3.Decode(file)
	case ".wav":
		streamer, format, err = wav.Decode(file)
	case ".flac":
		streamer, format, err = flac.Decode(file)
	default:
		file.Close()
		return fmt.Errorf("unsupported file format: %s", ext)
	}

	if err != nil {
		file.Close()
		return fmt.Errorf("failed to decode audio: %w", err)
	}

	// Initialize speaker if not already done or if format changed
	if ap.format.SampleRate != format.SampleRate {
		speaker.Init(format.SampleRate, format.SampleRate.N(time.Second/10))
		ap.format = format
	}

	// Create control and volume wrappers
	ap.ctrl = &beep.Ctrl{Streamer: streamer, Paused: true}
	ap.volume = &beep.Volume{Streamer: ap.ctrl, Base: 2, Volume: 0, Silent: false}

	ap.streamer = streamer
	ap.currentFile = filename
	ap.isPlaying = false
	ap.isPaused = true

	// Calculate duration
	ap.duration = format.SampleRate.D(streamer.Len())

	return nil
}

// Play starts or resumes playback
func (ap *AudioPlayer) Play() error {
	ap.mu.Lock()
	defer ap.mu.Unlock()

	if ap.streamer == nil {
		return fmt.Errorf("no file loaded")
	}

	if !ap.isPlaying {
		// Start playback with position tracking
		speaker.Play(beep.Seq(ap.volume, beep.Callback(func() {
			ap.mu.Lock()
			ap.isPlaying = false
			ap.isPaused = false
			ap.mu.Unlock()
			ap.onSongEnd()
		})))
		ap.isPlaying = true

		// Start position tracking goroutine
		go ap.trackPosition()
	}

	ap.ctrl.Paused = false
	ap.isPaused = false

	return nil
}

// Pause pauses playback
func (ap *AudioPlayer) Pause() {
	ap.mu.Lock()
	defer ap.mu.Unlock()

	if ap.ctrl != nil {
		ap.ctrl.Paused = true
		ap.isPaused = true
	}
}

// Stop stops playback and resets position
func (ap *AudioPlayer) Stop() {
	ap.mu.Lock()
	defer ap.mu.Unlock()

	if ap.ctrl != nil {
		speaker.Clear()
		ap.isPlaying = false
		ap.isPaused = false
		
		// Reset position to beginning
		if ap.streamer != nil {
			ap.streamer.Seek(0)
			ap.position = 0
		}
	}
}

// SetVolume sets the playback volume (0.0 to 1.0)
func (ap *AudioPlayer) SetVolume(volume float64) {
	ap.mu.Lock()
	defer ap.mu.Unlock()

	if ap.volume != nil {
		// Convert 0-1 range to beep's logarithmic scale
		if volume <= 0 {
			ap.volume.Silent = true
		} else {
			ap.volume.Silent = false
			// Convert linear volume to logarithmic (beep uses base 2)
			ap.volume.Volume = (volume - 1) * 5 // Approximate conversion
		}
	}
}

// Seek sets the playback position
func (ap *AudioPlayer) Seek(position time.Duration) error {
	ap.mu.Lock()
	defer ap.mu.Unlock()

	if ap.streamer == nil {
		return fmt.Errorf("no file loaded")
	}

	// Convert time to sample position
	samplePos := ap.format.SampleRate.N(position)
	if samplePos < 0 {
		samplePos = 0
	}
	if samplePos >= ap.streamer.Len() {
		samplePos = ap.streamer.Len() - 1
	}

	err := ap.streamer.Seek(samplePos)
	if err != nil {
		return fmt.Errorf("failed to seek: %w", err)
	}

	ap.position = position
	return nil
}

// GetPosition returns the current playback position
func (ap *AudioPlayer) GetPosition() time.Duration {
	ap.mu.RLock()
	defer ap.mu.RUnlock()
	return ap.position
}

// GetDuration returns the total duration of the current track
func (ap *AudioPlayer) GetDuration() time.Duration {
	ap.mu.RLock()
	defer ap.mu.RUnlock()
	return ap.duration
}

// IsPlaying returns true if audio is currently playing
func (ap *AudioPlayer) IsPlaying() bool {
	ap.mu.RLock()
	defer ap.mu.RUnlock()
	return ap.isPlaying && !ap.isPaused
}

// IsPaused returns true if audio is paused
func (ap *AudioPlayer) IsPaused() bool {
	ap.mu.RLock()
	defer ap.mu.RUnlock()
	return ap.isPaused
}

// GetCurrentFile returns the path of the currently loaded file
func (ap *AudioPlayer) GetCurrentFile() string {
	ap.mu.RLock()
	defer ap.mu.RUnlock()
	return ap.currentFile
}

// GetSongInfo returns information about the current song
func (ap *AudioPlayer) GetSongInfo() SongInfo {
	ap.mu.RLock()
	defer ap.mu.RUnlock()

	filename := filepath.Base(ap.currentFile)
	// Remove extension for title
	title := strings.TrimSuffix(filename, filepath.Ext(filename))

	return SongInfo{
		Title:    title,
		Artist:   "Unknown Artist", // Could be enhanced with metadata parsing
		Duration: ap.duration,
		Filename: filename,
	}
}

// SetOnPositionChange sets the callback for position updates
func (ap *AudioPlayer) SetOnPositionChange(callback func(time.Duration)) {
	ap.mu.Lock()
	defer ap.mu.Unlock()
	ap.onPositionChange = callback
}

// SetOnSongEnd sets the callback for when a song ends
func (ap *AudioPlayer) SetOnSongEnd(callback func()) {
	ap.mu.Lock()
	defer ap.mu.Unlock()
	ap.onSongEnd = callback
}

// trackPosition runs in a goroutine to track playback position
func (ap *AudioPlayer) trackPosition() {
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()

	for range ticker.C {
		ap.mu.Lock()
		if !ap.isPlaying || ap.isPaused {
			ap.mu.Unlock()
			return
		}

		// Update position based on speaker position
		speaker.Lock()
		speakerPos := speaker.Position()
		speaker.Unlock()

		ap.position = ap.format.SampleRate.D(speakerPos)
		callback := ap.onPositionChange
		ap.mu.Unlock()

		callback(ap.position)
	}
}

// Close cleans up the audio player
func (ap *AudioPlayer) Close() {
	ap.mu.Lock()
	defer ap.mu.Unlock()

	if ap.streamer != nil {
		speaker.Clear()
		ap.streamer.Close()
	}
}
