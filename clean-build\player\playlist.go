package player

import (
	"fmt"
	"path/filepath"
	"strings"
)

// PlaylistItem represents a single item in the playlist
type PlaylistItem struct {
	Path     string
	Filename string
	Title    string
}

// Playlist manages a list of audio files
type Playlist struct {
	items       []PlaylistItem
	currentIndex int
	onItemChange func(PlaylistItem)
}

// NewPlaylist creates a new playlist
func NewPlaylist() *Playlist {
	return &Playlist{
		items:        make([]PlaylistItem, 0),
		currentIndex: -1,
		onItemChange: func(PlaylistItem) {},
	}
}

// AddFile adds a single file to the playlist
func (p *Playlist) AddFile(filePath string) error {
	if !isAudioFile(filePath) {
		return fmt.Errorf("unsupported file format: %s", filepath.Ext(filePath))
	}

	filename := filepath.Base(filePath)
	title := strings.TrimSuffix(filename, filepath.Ext(filename))

	item := PlaylistItem{
		Path:     filePath,
		Filename: filename,
		Title:    title,
	}

	p.items = append(p.items, item)

	// If this is the first item, set it as current
	if len(p.items) == 1 {
		p.currentIndex = 0
		p.onItemChange(item)
	}

	return nil
}

// AddFiles adds multiple files to the playlist
func (p *Playlist) AddFiles(filePaths []string) error {
	for _, path := range filePaths {
		if err := p.AddFile(path); err != nil {
			// Continue adding other files even if one fails
			continue
		}
	}
	return nil
}

// RemoveItem removes an item from the playlist by index
func (p *Playlist) RemoveItem(index int) error {
	if index < 0 || index >= len(p.items) {
		return fmt.Errorf("index out of range")
	}

	// Remove the item
	p.items = append(p.items[:index], p.items[index+1:]...)

	// Adjust current index
	if index < p.currentIndex {
		p.currentIndex--
	} else if index == p.currentIndex {
		// Current item was removed
		if p.currentIndex >= len(p.items) {
			p.currentIndex = len(p.items) - 1
		}
		if p.currentIndex >= 0 {
			p.onItemChange(p.items[p.currentIndex])
		}
	}

	return nil
}

// Clear removes all items from the playlist
func (p *Playlist) Clear() {
	p.items = make([]PlaylistItem, 0)
	p.currentIndex = -1
}

// GetItems returns all playlist items
func (p *Playlist) GetItems() []PlaylistItem {
	return p.items
}

// GetCurrentItem returns the currently selected item
func (p *Playlist) GetCurrentItem() (PlaylistItem, error) {
	if p.currentIndex < 0 || p.currentIndex >= len(p.items) {
		return PlaylistItem{}, fmt.Errorf("no current item")
	}
	return p.items[p.currentIndex], nil
}

// GetCurrentIndex returns the index of the current item
func (p *Playlist) GetCurrentIndex() int {
	return p.currentIndex
}

// SetCurrentIndex sets the current item by index
func (p *Playlist) SetCurrentIndex(index int) error {
	if index < 0 || index >= len(p.items) {
		return fmt.Errorf("index out of range")
	}
	p.currentIndex = index
	p.onItemChange(p.items[p.currentIndex])
	return nil
}

// Next moves to the next item in the playlist
func (p *Playlist) Next() (PlaylistItem, error) {
	if len(p.items) == 0 {
		return PlaylistItem{}, fmt.Errorf("playlist is empty")
	}

	p.currentIndex++
	if p.currentIndex >= len(p.items) {
		p.currentIndex = 0 // Loop back to beginning
	}

	item := p.items[p.currentIndex]
	p.onItemChange(item)
	return item, nil
}

// Previous moves to the previous item in the playlist
func (p *Playlist) Previous() (PlaylistItem, error) {
	if len(p.items) == 0 {
		return PlaylistItem{}, fmt.Errorf("playlist is empty")
	}

	p.currentIndex--
	if p.currentIndex < 0 {
		p.currentIndex = len(p.items) - 1 // Loop to end
	}

	item := p.items[p.currentIndex]
	p.onItemChange(item)
	return item, nil
}

// HasNext returns true if there's a next item
func (p *Playlist) HasNext() bool {
	return len(p.items) > 0 && p.currentIndex < len(p.items)-1
}

// HasPrevious returns true if there's a previous item
func (p *Playlist) HasPrevious() bool {
	return len(p.items) > 0 && p.currentIndex > 0
}

// IsEmpty returns true if the playlist is empty
func (p *Playlist) IsEmpty() bool {
	return len(p.items) == 0
}

// Size returns the number of items in the playlist
func (p *Playlist) Size() int {
	return len(p.items)
}

// SetOnItemChange sets the callback for when the current item changes
func (p *Playlist) SetOnItemChange(callback func(PlaylistItem)) {
	p.onItemChange = callback
}

// isAudioFile checks if the file extension is supported
func isAudioFile(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	supportedFormats := []string{".mp3", ".wav", ".flac"}
	
	for _, format := range supportedFormats {
		if ext == format {
			return true
		}
	}
	return false
}

// GetSupportedFormats returns a list of supported audio formats
func GetSupportedFormats() []string {
	return []string{".mp3", ".wav", ".flac"}
}
