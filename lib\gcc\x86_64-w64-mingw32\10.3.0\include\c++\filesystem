// <filesystem> -*- C++ -*-

// Copyright (C) 2014-2020 Free Software Foundation, Inc.
//
// This file is part of the GNU ISO C++ Library.  This library is free
// software; you can redistribute it and/or modify it under the
// terms of the GNU General Public License as published by the
// Free Software Foundation; either version 3, or (at your option)
// any later version.

// This library is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.

// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.

// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

/** @file include/filesystem
 *  This is a Standard C++ Library header.
 *  @ingroup filesystem
 */

#ifndef _GLIBCXX_FILESYSTEM
#define _GLIBCXX_FILESYSTEM 1

#pragma GCC system_header

#if __cplusplus >= 201703L

/**
 * @defgroup filesystem File System
 *
 * Utilities for performing operations on file systems and their components,
 * such as paths, regular files, and directories.
 */

#include <bits/fs_fwd.h>
#include <bits/fs_path.h>
#include <bits/fs_dir.h>
#include <bits/fs_ops.h>

#define __cpp_lib_filesystem 201703

#endif // C++17

#endif // _GLIBCXX_FILESYSTEM
