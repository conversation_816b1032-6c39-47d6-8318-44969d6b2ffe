// -*- C++ -*-

// Copyright (C) 2007-2020 Free Software Foundation, Inc.
//
// This file is part of the GNU ISO C++ Library.  This library is free
// software; you can redistribute it and/or modify it under the terms
// of the GNU General Public License as published by the Free Software
// Foundation; either version 3, or (at your option) any later
// version.

// This library is distributed in the hope that it will be useful, but
// WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
// General Public License for more details.

// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.

// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

/** @file parallel/parallel.h
 *  @brief End-user include file. Provides advanced settings and
 *  tuning options.
 *  This file is a GNU parallel extension to the Standard C++ Library.
 */

// Written by <PERSON> and <PERSON>.

#ifndef _GLIBCXX_PARA<PERSON><PERSON>_PARALLEL_H
#define _GLIBCXX_PARALLEL_PARALLEL_H 1

#include <parallel/features.h>
#include <parallel/compiletime_settings.h>
#include <parallel/types.h>
#include <parallel/tags.h>
#include <parallel/settings.h>

#endif /* _GLIBCXX_PARALLEL_PARALLEL_H */
