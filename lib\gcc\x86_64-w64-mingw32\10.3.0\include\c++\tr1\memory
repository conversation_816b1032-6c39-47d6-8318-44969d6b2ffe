// <tr1/memory> -*- C++ -*-

// Copyright (C) 2005-2020 Free Software Foundation, Inc.
//
// This file is part of the GNU ISO C++ Library.  This library is free
// software; you can redistribute it and/or modify it under the
// terms of the GNU General Public License as published by the
// Free Software Foundation; either version 3, or (at your option)
// any later version.

// This library is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.

// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.

// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

/**
 * @file tr1/memory
 * This is a TR1 C++ Library header. 
 */

#ifndef _GLIBCXX_TR1_MEMORY
#define _GLIBCXX_TR1_MEMORY 1

#pragma GCC system_header

#if defined(_GLIBCXX_INCLUDE_AS_CXX11)
#  error TR1 header cannot be included from C++11 header
#endif

#include <memory>
#include <exception>        	// std::exception
#include <typeinfo>         	// std::type_info in get_deleter
#include <bits/stl_algobase.h>  // std::swap
#include <iosfwd>           	// std::basic_ostream
#include <ext/atomicity.h>
#include <ext/concurrence.h>
#include <bits/functexcept.h>
#include <bits/stl_function.h> 	// std::less
#include <debug/debug.h>
#include <tr1/type_traits>
#include <tr1/shared_ptr.h>

#endif // _GLIBCXX_TR1_MEMORY
