package player

import (
	"fmt"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"
)

// MusicPlayerUI represents the main UI for the music player
type MusicPlayerUI struct {
	app      fyne.App
	window   fyne.Window
	player   *AudioPlayer
	playlist *Playlist

	// UI Components
	playPauseBtn *widget.Button
	stopBtn      *widget.Button
	prevBtn      *widget.Button
	nextBtn      *widget.Button
	openBtn      *widget.Button

	progressBar   *widget.ProgressBar
	progressLabel *widget.Label
	volumeSlider  *widget.Slider

	songTitleLabel    *widget.Label
	songArtistLabel   *widget.Label
	songDurationLabel *widget.Label

	playlistWidget *widget.List

	isUpdatingProgress bool
}

// NewMusicPlayerUI creates a new music player UI
func NewMusicPlayerUI() *MusicPlayerUI {
	a := app.New()
	a.SetIcon(nil) // You can set an icon here

	w := a.NewWindow("Go Music Player")
	w.Resize(fyne.NewSize(800, 600))
	w.CenterOnScreen()

	ui := &MusicPlayerUI{
		app:      a,
		window:   w,
		player:   NewAudioPlayer(),
		playlist: NewPlaylist(),
	}

	ui.setupUI()
	ui.setupCallbacks()

	return ui
}

// setupUI creates and arranges all UI components
func (ui *MusicPlayerUI) setupUI() {
	// Control buttons
	ui.playPauseBtn = widget.NewButton("Play", ui.onPlayPause)
	ui.stopBtn = widget.NewButton("Stop", ui.onStop)
	ui.prevBtn = widget.NewButton("Previous", ui.onPrevious)
	ui.nextBtn = widget.NewButton("Next", ui.onNext)
	ui.openBtn = widget.NewButton("Open Files", ui.onOpenFiles)

	// Progress controls
	ui.progressBar = widget.NewProgressBar()
	ui.progressLabel = widget.NewLabel("00:00 / 00:00")

	// Volume control
	ui.volumeSlider = widget.NewSlider(0, 1)
	ui.volumeSlider.Value = 0.7
	ui.volumeSlider.OnChanged = ui.onVolumeChanged
	ui.player.SetVolume(0.7)

	// Song information
	ui.songTitleLabel = widget.NewLabel("No song loaded")
	ui.songTitleLabel.TextStyle = fyne.TextStyle{Bold: true}
	ui.songArtistLabel = widget.NewLabel("")
	ui.songDurationLabel = widget.NewLabel("")

	// Playlist
	ui.playlistWidget = widget.NewList(
		func() int {
			return ui.playlist.Size()
		},
		func() fyne.CanvasObject {
			return widget.NewLabel("Template")
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			items := ui.playlist.GetItems()
			if id < len(items) {
				obj.(*widget.Label).SetText(items[id].Title)
			}
		},
	)
	ui.playlistWidget.OnSelected = ui.onPlaylistItemSelected

	// Layout
	controlsContainer := container.NewHBox(
		ui.prevBtn,
		ui.playPauseBtn,
		ui.stopBtn,
		ui.nextBtn,
		widget.NewSeparator(),
		ui.openBtn,
	)

	progressContainer := container.NewBorder(
		nil, nil,
		ui.progressLabel,
		container.NewHBox(widget.NewLabel("Volume:"), ui.volumeSlider),
		ui.progressBar,
	)

	songInfoContainer := container.NewVBox(
		ui.songTitleLabel,
		ui.songArtistLabel,
		ui.songDurationLabel,
	)

	leftPanel := container.NewBorder(
		songInfoContainer,
		container.NewVBox(controlsContainer, progressContainer),
		nil, nil,
		widget.NewCard("Now Playing", "", container.NewVBox()),
	)

	rightPanel := widget.NewCard("Playlist", "", ui.playlistWidget)

	mainContainer := container.NewHSplit(leftPanel, rightPanel)
	mainContainer.SetOffset(0.6) // 60% for left panel, 40% for playlist

	ui.window.SetContent(mainContainer)
}

// setupCallbacks sets up event callbacks
func (ui *MusicPlayerUI) setupCallbacks() {
	// Player callbacks
	ui.player.SetOnPositionChange(ui.onPositionUpdate)
	ui.player.SetOnSongEnd(ui.onSongEnd)

	// Playlist callbacks
	ui.playlist.SetOnItemChange(ui.onPlaylistItemChange)

	// Window close callback
	ui.window.SetCloseIntercept(func() {
		ui.player.Close()
		ui.app.Quit()
	})
}

// Event handlers
func (ui *MusicPlayerUI) onPlayPause() {
	if ui.player.IsPlaying() {
		ui.player.Pause()
		ui.playPauseBtn.SetText("Play")
	} else {
		if err := ui.player.Play(); err != nil {
			dialog.ShowError(err, ui.window)
		} else {
			ui.playPauseBtn.SetText("Pause")
		}
	}
}

func (ui *MusicPlayerUI) onStop() {
	ui.player.Stop()
	ui.playPauseBtn.SetText("Play")
	ui.progressBar.SetValue(0)
	ui.updateProgressLabel(0, ui.player.GetDuration())
}

func (ui *MusicPlayerUI) onPrevious() {
	if item, err := ui.playlist.Previous(); err == nil {
		ui.loadAndPlaySong(item.Path)
	}
}

func (ui *MusicPlayerUI) onNext() {
	if item, err := ui.playlist.Next(); err == nil {
		ui.loadAndPlaySong(item.Path)
	}
}

func (ui *MusicPlayerUI) onOpenFiles() {
	dialog.ShowFileOpen(func(reader fyne.URIReadCloser, err error) {
		if err != nil {
			dialog.ShowError(err, ui.window)
			return
		}
		if reader == nil {
			return
		}
		defer reader.Close()

		uri := reader.URI()
		if uri != nil {
			path := uri.Path()
			if err := ui.playlist.AddFile(path); err != nil {
				dialog.ShowError(err, ui.window)
			} else {
				ui.playlistWidget.Refresh()
				// If this is the first file, load it
				if ui.playlist.Size() == 1 {
					ui.loadAndPlaySong(path)
				}
			}
		}
	}, ui.window)
}

func (ui *MusicPlayerUI) onVolumeChanged(value float64) {
	ui.player.SetVolume(value)
}

func (ui *MusicPlayerUI) onPlaylistItemSelected(id widget.ListItemID) {
	if err := ui.playlist.SetCurrentIndex(id); err == nil {
		if item, err := ui.playlist.GetCurrentItem(); err == nil {
			ui.loadAndPlaySong(item.Path)
		}
	}
}

func (ui *MusicPlayerUI) onPositionUpdate(position time.Duration) {
	duration := ui.player.GetDuration()
	if duration > 0 {
		ui.isUpdatingProgress = true
		ui.progressBar.SetValue(float64(position) / float64(duration))
		ui.isUpdatingProgress = false
		ui.updateProgressLabel(position, duration)
	}
}

func (ui *MusicPlayerUI) onSongEnd() {
	// Auto-play next song if available
	if ui.playlist.HasNext() {
		ui.onNext()
	} else {
		ui.playPauseBtn.SetText("Play")
	}
}

func (ui *MusicPlayerUI) onPlaylistItemChange(item PlaylistItem) {
	ui.updateSongInfo(item)
}

// Helper methods
func (ui *MusicPlayerUI) loadAndPlaySong(path string) {
	if err := ui.player.LoadFile(path); err != nil {
		dialog.ShowError(err, ui.window)
		return
	}

	if err := ui.player.Play(); err != nil {
		dialog.ShowError(err, ui.window)
	} else {
		ui.playPauseBtn.SetText("Pause")
	}
}

func (ui *MusicPlayerUI) updateSongInfo(item PlaylistItem) {
	ui.songTitleLabel.SetText(item.Title)
	ui.songArtistLabel.SetText("Unknown Artist")

	songInfo := ui.player.GetSongInfo()
	ui.songDurationLabel.SetText(ui.formatDuration(songInfo.Duration))
}

func (ui *MusicPlayerUI) updateProgressLabel(position, duration time.Duration) {
	posStr := ui.formatDuration(position)
	durStr := ui.formatDuration(duration)
	ui.progressLabel.SetText(fmt.Sprintf("%s / %s", posStr, durStr))
}

func (ui *MusicPlayerUI) formatDuration(d time.Duration) string {
	minutes := int(d.Minutes())
	seconds := int(d.Seconds()) % 60
	return fmt.Sprintf("%02d:%02d", minutes, seconds)
}

// Run starts the application
func (ui *MusicPlayerUI) Run() {
	ui.window.ShowAndRun()
}
